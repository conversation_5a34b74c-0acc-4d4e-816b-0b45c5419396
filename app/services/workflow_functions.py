# app/services/workflow_service.py
import grpc
import json
import structlog
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.workflow import Workflow, WorkflowTemplate
from app.models.workflow_rating import WorkflowRating
from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
)
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum
from app.utils.json_validator import validate_transition_schema
from app.utils.file_upload import GCSUploadService
from app.utils.kafka.kafka_service import KafkaProducer
from app.utils.workflow_data_transformer import extract_mcp_and_component_nodes_exact_format

# Initialize structured logger
logger = structlog.get_logger()


class WorkflowFunctions(workflow_pb2_grpc.WorkflowServiceServicer):
    def __init__(self):
        """Initialize the WorkflowService with a KafkaProducer instance"""
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def createWorkflow(
        self, request: workflow_pb2.CreateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CreateWorkflowResponse:
        db = SessionLocal()
        logger.info("create_workflow_request", name=request.name)
        try:
            # Parse workflow_data from string to JSON
            print("[DEBUG] Attempting to parse workflow_data JSON")
            try:
                workflow_data = json.loads(request.workflow_data)
                print("[DEBUG] Successfully parsed workflow_data JSON")
            except json.JSONDecodeError as e:
                print(f"[DEBUG] JSON parsing failed: {str(e)}")
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid JSON format in workflow_data")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message="Invalid JSON format in workflow_data"
                )
            
            try: 
                available_nodes = extract_mcp_and_component_nodes_exact_format(workflow_data)
                print("[DEBUG] Available nodes:", available_nodes)
            except Exception as e:
                print(f"[DEBUG] Available nodes extraction failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Available nodes extraction failed: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"Available nodes extraction failed: {str(e)}"
                )

            # Upload original workflow to GCS
            print("[DEBUG] Uploading workflow to GCS")
            try:
                file_upload = GCSUploadService()
                gcs_response = file_upload.upload_json_as_file(workflow_data, "workflow_builders")
                builder_url = gcs_response.get("publicUrl")
                print("[DEBUG] GCS upload successful, got builder_url")
                if not builder_url:
                    print("[DEBUG] Failed to get public URL from GCS response")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details("Failed to obtain public URL from GCS for builder workflow")
                    return workflow_pb2.CreateWorkflowResponse(
                        success=False,
                        message="Failed to obtain public URL from GCS for builder workflow",
                    )
            except Exception as e:
                print(f"[DEBUG] GCS upload failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"GCS upload failed for builder workflow: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"GCS upload failed for builder workflow: {str(e)}"
                )

            # Convert workflow to transition schema
            print("[DEBUG] Converting workflow to transition schema")
            try:
                converted_workflow = convert_workflow_to_transition_schema(workflow_data)
                print("[DEBUG] Workflow conversion successful")
            except Exception as e:
                print(f"[DEBUG] Workflow conversion failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Workflow schema conversion failed: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"Workflow schema conversion failed: {str(e)}"
                )

            # Validate converted workflow against transition_schema.json
            print("[DEBUG] Validating against transition_schema.json")
            try:
                validate_transition_schema(
                    data_input=converted_workflow,
                    schema_path="app/utils/shared/json_schemas/transition_schema.json",
                )
                print("[DEBUG] Transition schema validation successful")
            except Exception as e:
                print(f"[DEBUG] Transition schema validation failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Transition schema validation failed: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"Transition schema validation failed: {str(e)}"
                )

            # Upload converted workflow to GCS
            print("[DEBUG] Uploading converted workflow to GCS")
            try:
                gcs_response = file_upload.upload_json_as_file(converted_workflow, "workflows")
                workflow_url = gcs_response.get("publicUrl")
                print("[DEBUG] Converted workflow upload successful")
                if not workflow_url:
                    print("[DEBUG] Failed to get public URL for converted workflow")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(
                        "Failed to obtain public URL from GCS for converted workflow"
                    )
                    return workflow_pb2.CreateWorkflowResponse(
                        success=False,
                        message="Failed to obtain public URL from GCS for converted workflow",
                    )
            except Exception as e:
                print(f"[DEBUG] Converted workflow GCS upload failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"GCS upload failed for converted workflow: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"GCS upload failed for converted workflow: {str(e)}"
                )

            # Create new workflow
            print("[DEBUG] Creating new workflow object")
            new_workflow = Workflow(
                # Primary Fields
                name=request.name,
                workflow_url=workflow_url,
                builder_url=builder_url,
                start_nodes=[json.loads(node_json) for node_json in request.start_nodes],
                available_nodes=available_nodes,
                # Access Control
                owner_id=request.owner.id,
                user_ids=[request.owner.id],
                owner_type=workflow_pb2.WorkflowOwnerType.Name(request.owner_type).lower(),
                # Template Reference
                workflow_template_id=None,  # Set to None for new workflows
                template_owner_id=None,  # Set to None for new workflows
                url=None,  # Set to None for new workflows
                is_imported=False,  # Default to False for new workflows
                # Additional Fields
                version="1.0.0",  # Default version for new workflows
                tags=list(request.tags) if request.tags else [],
            )

            print("[DEBUG] Adding workflow to database")
            db.add(new_workflow)
            db.commit()
            db.refresh(new_workflow)
            print("[DEBUG] Workflow saved to database successfully")

            # Send Kafka notification
            print("[DEBUG] Sending Kafka notification")
            print(f"[DEBUG] WORKFLOW CREATED {new_workflow.id}")
            # try:
            #     self.kafka_producer.send_email_event_unified(
            #         email_type=SendEmailTypeEnum.WORKFLOW_CREATED.value,
            #         data={
            #             "emailId": request.owner.email,
            #             "userName": request.owner.full_name,
            #             "userId": request.owner.id,
            #             "fcmToken": request.owner.fcm_token,
            #             "workflowId": new_workflow.id,
            #             "workflowName": new_workflow.name,
            #             "title": "New Workflow Created",
            #             "body": f"Your workflow '{new_workflow.name}' has been created successfully.",
            #             "link": f"{settings.FRONTEND_URL}/workflows/{new_workflow.id}",
            #             "logo": f"{settings.FRONTEND_URL}/assets/logo.png",
            #         },
            #         action=["sendNotification", "sendWorkflowEmail"],
            #     )
            #     print("[DEBUG] Kafka notification sent successfully")
            # except Exception as e:
            #     print(f"[DEBUG] Failed to send Kafka notification: {str(e)}")
            #     # Continue even if notification fails

            print("[DEBUG] Workflow creation completed successfully")
            return workflow_pb2.CreateWorkflowResponse(
                success=True,
                message=f"Workflow {request.name} created successfully",
                workflow=self._workflow_to_protobuf(new_workflow),
            )

        except Exception as e:
            print(f"[DEBUG] Unexpected error in createWorkflow: {str(e)}")
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("workflow_creation_failed", error=str(e))
            return workflow_pb2.CreateWorkflowResponse(
                success=False, message=f"Workflow creation failed: {str(e)}"
            )
        finally:
            print("[DEBUG] Closing database connection")
            db.close()

    def getWorkflow(
        self, request: workflow_pb2.GetWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.WorkflowResponse:
        """
        Retrieve a workflow by its ID.

        Args:
            request (workflow_pb2.GetWorkflowRequest): The request containing the workflow ID and optional user_id.
            context (grpc.ServicerContext): The gRPC context for handling the request.

        Returns:
            workflow_pb2.WorkflowResponse: The response containing the workflow details if found.

        Raises:
            grpc.RpcError: If the workflow is not found or an unexpected error occurs.
        """
        db = self.get_db()
        logger.info(
            "get_workflow_request",
            workflow_id=request.id,
            user_id=request.user_id if request.HasField("user_id") else None,
        )
        try:
            # Start with base query
            query = db.query(Workflow).filter(Workflow.id == request.id)

            # If user_id is provided, add filter to match owner_id
            if request.HasField("user_id") and request.user_id:
                logger.info("filtering_by_user_id", user_id=request.user_id)
                query = query.filter(Workflow.owner_id == request.user_id)

            # Execute query
            workflow = query.first()

            if workflow is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found")
                return workflow_pb2.WorkflowResponse(success=False, message="Workflow not found")

            logger.info("workflow_retrieved", workflow_id=workflow.id)
            workflow11 = self._workflow_to_protobuf(workflow)
            print(f"[WORKFLOW TO PROTOBUF] {workflow11}")
            return workflow_pb2.WorkflowResponse(
                success=True,
                message=f"Workflow {workflow.name} retrieved successfully",
                workflow=self._workflow_to_protobuf(workflow),
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("internal_server_error", error=e)
            return workflow_pb2.WorkflowResponse(success=False, message="Internal Server Error")
        finally:
            db.close()

    def deleteWorkflow(
        self, request: workflow_pb2.DeleteWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.DeleteWorkflowResponse:
        """
        Deletes a workflow from the database.

        Args:
            request (workflow_pb2.DeleteWorkflowRequest): The request object containing the workflow ID to be deleted.
            context (grpc.ServicerContext): The gRPC context for handling request status and errors.

        Returns:
            workflow_pb2.DeleteWorkflowResponse: A response object indicating the success or failure of the deletion.

        Raises:
            grpc.StatusCode.NOT_FOUND: If the specified workflow does not exist.
            grpc.StatusCode.INTERNAL: If an internal error occurs during the deletion process.
        """
        db = self.get_db()
        logger.info("delete_workflow_request", workflow_id=request.id)
        try:
            workflow = db.query(Workflow).filter(Workflow.id == request.id).first()
            if workflow is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found")
                return workflow_pb2.DeleteWorkflowResponse(
                    success=False, message=f"Workflow not found"
                )

            db.delete(workflow)
            db.commit()
            logger.info("workflow_deleted", workflow_id=workflow.id)

            # # Send Kafka event for notifications
            # self.kafka_producer.send_email_event_unified(
            #     email_type=SendEmailTypeEnum.WORKFLOW_DELETED.value,
            #     data={
            #         "emailId": request.owner.email,
            #         "userName": request.owner.full_name,
            #         "userId": request.owner.id,
            #         "fcmToken": request.owner.fcm_token,
            #         "workflowId": request.id,
            #         "workflowName": workflow.name,
            #         "title": "Workflow Deleted",
            #         "body": f"Your workflow '{workflow.name}' has been deleted.",
            #         "link": f"{settings.FRONTEND_URL}/workflows",
            #         "logo": f"{settings.FRONTEND_URL}/assets/logo.png",
            #     },
            #     action=["sendNotification", "sendWorkflowEmail"],
            # )

            logger.info("kafka_event_sent", event_type="WORKFLOW_DELETED")

            return workflow_pb2.DeleteWorkflowResponse(
                success=True, message=f"Workflow {workflow.name} deleted successfully"
            )
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("internal_server_error", error=e)
            return workflow_pb2.DeleteWorkflowResponse(
                success=False, message="Internal server error"
            )
        finally:
            db.close()

    def listWorkflows(
        self, request: workflow_pb2.ListWorkflowsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ListWorkflowsResponse:
        """
        Retrieve a paginated list of workflows with optional filtering by user_id.

        Args:
            request (workflow_pb2.ListWorkflowsRequest): The request object containing pagination details and filters.
            context (grpc.ServicerContext): The gRPC context for handling errors and metadata.

        Returns:
            workflow_pb2.ListWorkflowsResponse: A response containing the list of workflows, total count, current page, and total pages.

        Raises:
            grpc.StatusCode.INTERNAL: If any error occurs during the database query.
        """
        db = self.get_db()
        page = request.page
        page_size = request.page_size
        logger.info(
            "list_workflows_request",
            page=page,
            page_size=page_size,
            user_id=request.user_id if request.HasField("user_id") else None,
        )
        try:
            # Start with a base query
            query = db.query(Workflow)

            # Apply user_id filter if provided
            if request.HasField("user_id") and request.user_id:
                logger.info("filtering_by_user_id", user_id=request.user_id)
                query = query.filter(Workflow.owner_id == request.user_id)

            # Apply filters if provided
            if request.category:
                category_value = workflow_pb2.WorkflowCategory.Name(request.category).lower()
                logger.info("filtering_by_category", category=category_value)
                query = query.filter(Workflow.category == category_value)

            if request.status:
                status_value = workflow_pb2.WorkflowStatus.Name(request.status).lower()
                logger.info("filtering_by_status", status=status_value)
                query = query.filter(Workflow.status == status_value)

            if request.visibility:
                visibility_value = workflow_pb2.WorkflowVisibility.Name(request.visibility).lower()
                logger.info("filtering_by_visibility", visibility=visibility_value)
                query = query.filter(Workflow.visibility == visibility_value)

            # Add search filter for name, description, and category
            if request.search:
                search_term = f"%{request.search}%"
                logger.info("filtering_by_search", search=request.search)
                query = query.filter(
                    db.or_(
                        Workflow.name.ilike(search_term),
                        Workflow.description.ilike(search_term),
                        Workflow.category.ilike(search_term),
                    )
                )

            # Add tags filter
            if request.tags:
                logger.info("filtering_by_tags", tags=request.tags)
                # Filter by tags (exact match for any tag in the list)
                query = query.filter(Workflow.tags.contains(request.tags))

            # Get total count with filters applied
            total = query.count()

            # Apply pagination
            workflows = query.offset((page - 1) * page_size).limit(page_size).all()

            total_pages = (total + page_size - 1) // page_size  # Calculate total pages

            workflow_list = [self._workflow_to_protobuf(workflow) for workflow in workflows]

            print(f"[DEBUG] Workflow list: {workflow_list}")

            logger.info("workflows_retrieved", total=total, page=page, total_pages=total_pages)

            return workflow_pb2.ListWorkflowsResponse(
                workflows=workflow_list,
                total=total,
                page=page,
                total_pages=total_pages,
            )
        except Exception as e:
            print(f"[DEBUG] Unexpected error in listWorkflows: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("Internal_server_error", error=e)
            return workflow_pb2.ListWorkflowsResponse()  # Return empty response on error
        finally:
            db.close()

    def listWorkflowsByUserId(
        self, request: workflow_pb2.ListWorkflowsByUserIdRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ListWorkflowsResponse:
        """
        Retrieve a paginated list of workflows for user only.

        Args:
            request (workflow_pb2.ListWorkflowsByUserIdRequest): The request object containing pagination details.
            context (grpc.ServicerContext): The gRPC context for handling errors and metadata.

        Returns:
            workflow_pb2.ListWorkflowsResponse: A response containing the list of workflows, total count, current page, and total pages.

        Raises:
            grpc.StatusCode.INTERNAL: If any error occurs during the database query.
        """
        db = self.get_db()
        logger.info("list_workflows_by_user_request", owner_id=request.owner_id)
        try:
            # Pagination setup
            page = request.page
            page_size = request.page_size
            offset = (page - 1) * page_size

            # Start with a base query
            query = db.query(Workflow)
            print(f"[DEBUG] List Workflows by User ID Request: {request.category}")
            print(
                f"[DEBUG] List Workflows by User ID Request: {workflow_pb2.WorkflowCategory.Name(request.category).lower()}"
            )
            # Apply filters if provided
            if request.category:
                category_value = workflow_pb2.WorkflowCategory.Name(request.category).lower()
                logger.info("filtering_by_category", category=category_value)
                query = query.filter(Workflow.category == category_value)

            if request.status:
                status_value = workflow_pb2.WorkflowStatus.Name(request.status).lower()
                logger.info("filtering_by_status", status=status_value)
                query = query.filter(Workflow.status == status_value)

            if request.visibility:
                visibility_value = workflow_pb2.WorkflowVisibility.Name(request.visibility).lower()
                logger.info("filtering_by_visibility", visibility=visibility_value)
                query = query.filter(Workflow.visibility == visibility_value)
            # Get total count before pagination
            total = query.count()

            logger.info("total workflows count for user_id", total=total)

            # Apply pagination
            workflows = query.offset(offset).limit(page_size).all()

            # Calculate total pages
            total_pages = (total + page_size - 1) // page_size

            # Convert workflows to protobuf objects
            protobuf_workflows = [self._workflow_to_protobuf(workflow) for workflow in workflows]

            logger.info("list_workflows_by_user_response", workflows=protobuf_workflows)

            return workflow_pb2.ListWorkflowsResponse(
                success=True,
                workflows=protobuf_workflows,
                total=total,
                page=page,
                total_pages=total_pages,
            )
        except Exception as e:
            print(f"[DEBUG] Unexpected error in listWorkflowsByUserId: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return workflow_pb2.ListWorkflowsResponse()
        finally:
            db.close()

    def getWorkflowsByIds(
        self, request: workflow_pb2.GetWorkflowsByIdsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetWorkflowsByIdsResponse:
        logger.info("get_workflows_by_ids_request", ids_count=len(request.ids))
        print(f"[DEBUG] gRPC Servicer: getWorkflowsByIds called with IDs: {request.ids}")

        if not request.ids:
            logger.warn("get_workflows_by_ids_request_empty_ids")
            return workflow_pb2.GetWorkflowsByIdsResponse(
                workflows=[], total=0, success=True, message="No IDs provided to fetch."
            )

        try:
            db = self.get_db()
            # Query Workflows by the provided IDs
            workflow_models = db.query(Workflow).filter(Workflow.id.in_(request.ids)).all()

            total = len(workflow_models)

            # Convert to protobuf format
            # You'll need a helper similar to _mcp_to_protobuf for workflows
            workflow_list_proto = [self._workflow_to_protobuf(wf) for wf in workflow_models]

            print(f"[DEBUG] Workflow list: {workflow_list_proto}")
            logger.info(
                "workflows_retrieved_by_ids",
                retrieved_count=len(workflow_list_proto),
                requested_count=len(request.ids),
            )

            return workflow_pb2.GetWorkflowsByIdsResponse(
                success=True,
                message=f"Successfully retrieved workflows.",
                workflows=workflow_list_proto,
                total=total,
            )
        except Exception as e:
            print(f"[DEBUG] Error fetching workflows by IDs: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error while fetching workflows by IDs: {str(e)}")
            return workflow_pb2.GetWorkflowsByIdsResponse(
                success=False, message="Internal server error."
            )
        # No finally block needed for db.close() due to context manager

    def _workflow_to_protobuf(self, workflow: Workflow) -> workflow_pb2.Workflow:
        """Convert a Workflow model instance to a protobuf Workflow message."""
        return workflow_pb2.Workflow(
            id=str(workflow.id),  # Convert UUID to string
            name=workflow.name,
            description=workflow.description,
            workflow_url=workflow.workflow_url,
            builder_url=workflow.builder_url,
            owner_id=workflow.owner_id,
            user_ids=workflow.user_ids if workflow.user_ids else [],
            owner_type=workflow.owner_type,
            start_nodes=(
                [json.dumps(node) for node in workflow.start_nodes] if workflow.start_nodes else []
            ),
            available_nodes=(
                [json.dumps(node) for node in workflow.available_nodes]
                if workflow.available_nodes
                else []
            ),
            # Template Reference fields - Always include these fields
            workflow_template_id=(
                str(workflow.workflow_template_id) if workflow.workflow_template_id else None
            ),
            template_owner_id=(
                str(workflow.template_owner_id) if workflow.template_owner_id else None
            ),
            url=workflow.url if workflow.url else None,
            is_imported=workflow.is_imported if workflow.is_imported else False,
            # Metadata
            is_changes_marketplace=(
                workflow.is_changes_marketplace if workflow.is_changes_marketplace else False
            ),
            version=workflow.version if workflow.version else None,
            visibility=workflow.visibility,
            category=workflow.category,
            tags=workflow.tags if workflow.tags else [],
            status=workflow.status,
            # Timestamps
            created_at=workflow.created_at.isoformat() if workflow.created_at else "",
            updated_at=workflow.updated_at.isoformat() if workflow.updated_at else "",
        )

    def _ensure_workflow_template_from_workflow(
        self,
        db,
        workflow: Workflow,
        create_new_if_missing=True,
    ) -> WorkflowTemplate | None:
        """
        Ensures a WorkflowTemplate exists for a given public workflow owned by the user,
        updates it, and returns it.
        If create_new_if_missing is False, it only attempts to find and update an existing one.
        """
        if not workflow.owner_id:  # Should not happen for a valid workflow
            logger.error(f"Workflow {workflow.id} has no owner_id, cannot manage template.")
            return None

        # Case 1: Workflow is already linked to a template owned by this user
        if workflow.workflow_template_id and workflow.template_owner_id == workflow.owner_id:
            template = (
                db.query(WorkflowTemplate)
                .filter(
                    WorkflowTemplate.id == workflow.workflow_template_id,
                    WorkflowTemplate.owner_id == workflow.owner_id,  # Double check ownership
                )
                .first()
            )
            if template:
                logger.info(
                    f"Found existing linked template {template.id} for workflow {workflow.id}"
                )
                # Update content from workflow
                template.name = workflow.name
                template.description = workflow.description or "No description provided."
                template.workflow_url = workflow.workflow_url
                template.builder_url = workflow.builder_url
                template.start_nodes = workflow.start_nodes
                template.available_nodes = workflow.available_nodes
                template.category = workflow.category
                template.tags = workflow.tags
                template.version = workflow.version or "1.0.0"
                template.status = WorkflowStatusEnum.ACTIVE  # Ensure it's active
                db.add(template)
                return template
            else:
                logger.warning(
                    f"Workflow {workflow.id} linked to template {workflow.workflow_template_id} "
                    f"but template not found or not owned by user. Will try to create new if allowed."
                )
                # Fall through to create new if create_new_if_missing is True

        # Case 2: Create a new template if allowed and no valid existing link
        if create_new_if_missing:
            logger.info(f"Creating new WorkflowTemplate for workflow {workflow.id}")
            new_template = WorkflowTemplate(
                name=workflow.name,
                description=workflow.description or "No description provided.",
                workflow_url=workflow.workflow_url,
                builder_url=workflow.builder_url,
                start_nodes=workflow.start_nodes,
                available_nodes=workflow.available_nodes,
                owner_id=workflow.owner_id,
                use_count=0,
                category=workflow.category,
                tags=workflow.tags,
                version=workflow.version or "1.0.0",
                status=WorkflowStatusEnum.ACTIVE,
            )
            db.add(new_template)
            db.flush()  # Ensure ID is available

            # Link workflow to this new template
            workflow.workflow_template_id = new_template.id
            workflow.template_owner_id = new_template.owner_id
            # workflow.is_changes_marketplace is handled by the calling function
            db.add(workflow)
            return new_template

        return None

    def toggleWorkflowVisibility(
        self, request: workflow_pb2.ToggleWorkflowVisibilityRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ToggleWorkflowVisibilityResponse:
        db = self.get_db()
        logger.info(
            "toggle_workflow_visibility_request",
            workflow_id=request.workflow_id,
            owner_id=request.owner.id,
        )
        try:
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()

            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found.")
                return workflow_pb2.ToggleWorkflowVisibilityResponse(
                    success=False, message="Workflow not found."
                )

            if workflow.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("You are not the owner of this workflow.")
                return workflow_pb2.ToggleWorkflowVisibilityResponse(
                    success=False, message="Permission denied. You are not the owner."
                )

            message = ""
            if workflow.visibility == WorkflowVisibilityEnum.PRIVATE:
                # --- Going from PRIVATE to PUBLIC ---
                # Constraint: Cannot publish if cloned from another's template and minimally altered
                if (
                    workflow.is_imported
                    and workflow.template_owner_id
                    and workflow.template_owner_id != workflow.owner_id
                ):
                    err_msg = (
                        "This workflow was created from a template owned by another party. "
                        "To share your version publicly, please ensure it is significantly customized. "
                        "Direct republishing of minimally-altered cloned templates is restricted."
                    )
                    context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                    context.set_details(err_msg)
                    return workflow_pb2.ToggleWorkflowVisibilityResponse(
                        success=False, message=err_msg
                    )

                workflow.visibility = WorkflowVisibilityEnum.PUBLIC
                workflow.is_changes_marketplace = True  # Default to True when making public

                # Ensure template exists/is_active and content is synced
                template = self._ensure_workflow_template_from_workflow(
                    db, workflow, create_new_if_missing=True
                )
                if not template:
                    # This should ideally not happen if _ensure_workflow_template_from_workflow works correctly
                    # or if the workflow owner_id was missing (pre-checked in helper)
                    db.rollback()
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details("Failed to create or link workflow template.")
                    return workflow_pb2.ToggleWorkflowVisibilityResponse(
                        success=False, message="Failed to prepare workflow for public visibility."
                    )

                message = f"Workflow '{workflow.name}' is now PUBLIC. Changes will sync to marketplace by default."

            elif workflow.visibility == WorkflowVisibilityEnum.PUBLIC:
                # --- Going from PUBLIC to PRIVATE ---
                workflow.visibility = WorkflowVisibilityEnum.PRIVATE
                # workflow.is_changes_marketplace = False # Optional: Reset this flag

                # Deactivate the linked template if it's owned by this user
                if (
                    workflow.workflow_template_id
                    and workflow.template_owner_id == workflow.owner_id
                ):
                    linked_template = (
                        db.query(WorkflowTemplate)
                        .filter(
                            WorkflowTemplate.id == workflow.workflow_template_id,
                            WorkflowTemplate.owner_id == workflow.owner_id,
                        )
                        .first()
                    )
                    if linked_template:
                        linked_template.status = WorkflowStatusEnum.INACTIVE
                        db.add(linked_template)
                        logger.info(
                            f"Deactivated template {linked_template.id} as workflow {workflow.id} made private."
                        )

                message = f"Workflow '{workflow.name}' is now PRIVATE."
            else:
                # Should not happen if visibility is always one of the defined enums
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details(
                    f"Workflow {workflow.id} has an unknown visibility state: {workflow.visibility}"
                )
                return workflow_pb2.ToggleWorkflowVisibilityResponse(
                    success=False, message="Workflow has an unknown visibility state."
                )

            db.commit()
            db.refresh(workflow)
            # if 'template' in locals() and template: db.refresh(template)
            # if 'linked_template' in locals() and linked_template: db.refresh(linked_template)

            return workflow_pb2.ToggleWorkflowVisibilityResponse(
                success=True,
                message=message,
                workflow=self._workflow_to_protobuf(workflow),  # Ensure this reflects new state
            )

        except Exception as e:
            db.rollback()
            logger.error("toggle_workflow_visibility_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to toggle workflow visibility: {str(e)}")
            return workflow_pb2.ToggleWorkflowVisibilityResponse(
                success=False, message=f"Failed to toggle workflow visibility: {str(e)}"
            )
        finally:
            db.close()

    def updateWorkflow(
        self, request: workflow_pb2.UpdateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UpdateWorkflowResponse:
        db = self.get_db()
        logger.info(
            "update_workflow_request", workflow_id=request.id, update_mask=request.update_mask.paths
        )

        try:
            workflow = db.query(Workflow).filter(Workflow.id == request.id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.id} not found")
                return workflow_pb2.UpdateWorkflowResponse(
                    success=False, message=f"Workflow with ID {request.id} not found"
                )

            # Ownership check (assuming request.owner is present and has an id)
            if not hasattr(request, "owner") or not request.owner.id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Owner information missing in request.")
                return workflow_pb2.UpdateWorkflowResponse(
                    success=False, message="Owner information missing."
                )

            if workflow.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("You are not the owner of this workflow.")
                return workflow_pb2.UpdateWorkflowResponse(
                    success=False, message="Permission denied."
                )

            file_upload_service = GCSUploadService()  # Initialize GCS service
            template_relevant_fields_changed = False

            # Process workflow_data only if it's in the update_mask
            if "workflow_data" in request.update_mask.paths:
                print("[DEBUG] 'workflow_data' is in update_mask. Processing...")
                if not request.workflow_data:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(
                        "workflow_data field is in update_mask but no data provided."
                    )
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message="workflow_data provided in mask but is empty."
                    )
                try:
                    parsed_workflow_data = json.loads(request.workflow_data)
                    print("[DEBUG] Successfully parsed workflow_data JSON for PATCH")
                except json.JSONDecodeError as e:
                    print(f"[DEBUG] JSON parsing failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Invalid JSON format in workflow_data")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message="Invalid JSON format in workflow_data"
                    )

                # Upload original workflow to GCS
                try:
                    gcs_response_builder = file_upload_service.upload_json_as_file(
                        parsed_workflow_data, "workflow_builders"
                    )
                    builder_url = gcs_response_builder.get("publicUrl")
                    if not builder_url:
                        raise Exception("Failed to get public URL from GCS for builder workflow")
                    workflow.builder_url = builder_url  # Update the model field
                    print(f"[DEBUG] GCS builder upload successful for PATCH: {builder_url}")
                except Exception as e:
                    # (Error handling as before, but specific to this block)
                    print(f"[DEBUG] GCS builder upload failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"GCS upload failed for builder workflow: {str(e)}")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message=f"GCS upload failed for builder workflow: {str(e)}"
                    )

                # Convert workflow to transition schema
                try:
                    converted_workflow = convert_workflow_to_transition_schema(parsed_workflow_data)
                    print("[DEBUG] Workflow conversion successful for PATCH")
                except Exception as e:
                    # (Error handling as before)
                    print(f"[DEBUG] Workflow conversion failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"Workflow schema conversion failed: {str(e)}")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message=f"Workflow schema conversion failed: {str(e)}"
                    )

                # Validate converted workflow
                try:
                    validate_transition_schema(
                        data_input=converted_workflow,
                        schema_path="app/utils/shared/json_schemas/transition_schema.json",
                    )
                    print("[DEBUG] Transition schema validation successful for PATCH")
                except Exception as e:
                    # (Error handling as before)
                    print(f"[DEBUG] Transition schema validation failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"Transition schema validation failed: {str(e)}")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message=f"Transition schema validation failed: {str(e)}"
                    )

                # Upload converted workflow to GCS
                try:
                    gcs_response_workflow = file_upload_service.upload_json_as_file(
                        converted_workflow, "workflows"
                    )
                    workflow_url = gcs_response_workflow.get("publicUrl")
                    if not workflow_url:
                        raise Exception("Failed to get public URL from GCS for converted workflow")
                    workflow.workflow_url = workflow_url  # Update the model field
                    print(
                        f"[DEBUG] Converted workflow GCS upload successful for PATCH: {workflow_url}"
                    )
                except Exception as e:
                    # (Error handling as before)
                    print(f"[DEBUG] Converted GCS workflow upload failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"GCS upload failed for converted workflow: {str(e)}")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message=f"GCS upload failed for converted workflow: {str(e)}"
                    )

                template_relevant_fields_changed = True

            # Update other fields based on FieldMask
            for field_path in request.update_mask.paths:
                if field_path == "name":
                    workflow.name = request.name
                elif field_path == "description":
                    workflow.description = request.description
                elif field_path == "start_nodes":
                    workflow.start_nodes = [
                        json.loads(node_json) for node_json in request.start_nodes
                    ]
                elif field_path == "user_ids":
                    workflow.user_ids = list(request.user_ids) if request.user_ids else []
                elif field_path == "visibility":
                    workflow.visibility = workflow_pb2.WorkflowVisibility.Name(
                        request.visibility
                    ).lower()
                elif field_path == "category":
                    workflow.category = workflow_pb2.WorkflowCategory.Name(request.category).lower()
                elif field_path == "tags":  # tags is repeated string in proto, array in DB
                    workflow.tags = list(request.tags) if request.tags else []
                elif field_path == "status":
                    workflow.status = workflow_pb2.WorkflowStatus.Name(request.status).lower()
                elif field_path == "version":
                    workflow.version = request.version
                elif field_path == "is_changes_marketplace" and request.HasField(
                    "is_changes_marketplace"
                ):
                    # This allows explicitly setting this flag via update.
                    # Ensure it only applies if workflow is public and owned by user for template context.
                    if (
                        workflow.visibility == WorkflowVisibilityEnum.PUBLIC
                        and workflow.workflow_template_id
                        and workflow.template_owner_id == workflow.owner_id
                    ):
                        workflow.is_changes_marketplace = request.is_changes_marketplace
                    else:
                        logger.info(
                            f"is_changes_marketplace update ignored for workflow {workflow.id} "
                            "as it's not a public, user-owned templated workflow."
                        )

            # Sync changes to the corresponding WorkflowTemplate if applicable
            if (
                template_relevant_fields_changed
                and workflow.visibility == WorkflowVisibilityEnum.PUBLIC
                and workflow.is_changes_marketplace
                and workflow.template_owner_id == workflow.owner_id
                and workflow.workflow_template_id
            ):
                logger.info(f"Syncing changes from public workflow {workflow.id} to its template.")
                # The helper function will find the template, update its content from workflow, and ensure it's active
                template = self._ensure_workflow_template_from_workflow(
                    db, workflow, create_new_if_missing=False
                )
                if not template:
                    logger.error(
                        f"Workflow {workflow.id} is set to sync, but its linked template "
                        f"{workflow.workflow_template_id} (owned by user) was not found or couldn't be updated. "
                        "This indicates a potential inconsistency."
                    )
                    # Decide on error handling: rollback, log, or proceed with workflow update only
                    # For now, let's proceed with workflow update but log the error.
                else:
                    db.add(template)  # Add to session if modified

            db.add(workflow)  # Add workflow to session
            db.commit()
            db.refresh(workflow)
            # if 'template' in locals() and template: db.refresh(template)

            return workflow_pb2.UpdateWorkflowResponse(
                success=True, message=f"Workflow {workflow.name} updated successfully"
            )

        except Exception as e:
            if db.is_active:
                db.rollback()
            logger.error("workflow_update_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error during workflow update: {str(e)}")
            return workflow_pb2.UpdateWorkflowResponse(
                success=False, message=f"Workflow update failed: {str(e)}"
            )
        finally:
            db.close()

    def getMarketplaceWorkflows(
        self, request: workflow_pb2.GetMarketplaceWorkflowsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetMarketplaceWorkflowsResponse:
        """
        Retrieves a paginated list of public workflow templates for the marketplace.

        Args:
            request: The request containing pagination, search, and filter parameters
            context: The gRPC context for handling errors

        Returns:
            Response containing the list of marketplace workflows and pagination metadata
        """
        db = self.get_db()
        logger.info(
            "get_marketplace_workflows_request",
            request=request,
        )

        try:
            # Start with a base query for public templates
            query = db.query(WorkflowTemplate).filter(
                WorkflowTemplate.visibility == WorkflowVisibilityEnum.PUBLIC,
                WorkflowTemplate.status == WorkflowStatusEnum.ACTIVE,
            )

            # Apply search filter if provided
            if request.HasField("search") and request.search:
                search_term = f"%{request.search}%"
                query = query.filter(
                    (WorkflowTemplate.name.ilike(search_term))
                    | (WorkflowTemplate.description.ilike(search_term))
                )

            # Apply category filter if provided
            if request.HasField("category") and request.category:
                category_value = workflow_pb2.WorkflowCategory.Name(request.category).lower()
                query = query.filter(WorkflowTemplate.category == category_value)

            # Apply tags filter if provided
            if request.tags:
                # Filter by tags (exact match for any tag in the list)
                query = query.filter(WorkflowTemplate.tags.contains(request.tags))

            # Apply sorting
            if request.HasField("sort_by") and request.sort_by:
                sort_by = request.sort_by
                if sort_by == "NEWEST":
                    query = query.order_by(WorkflowTemplate.created_at.desc())
                elif sort_by == "OLDEST":
                    query = query.order_by(WorkflowTemplate.created_at.asc())
                elif sort_by == "MOST_POPULAR":
                    query = query.order_by(WorkflowTemplate.use_count.desc())
                elif sort_by == "HIGHEST_RATED":
                    query = query.order_by(WorkflowTemplate.average_rating.desc())
                else:
                    # Default to newest
                    query = query.order_by(WorkflowTemplate.created_at.desc())
            else:
                # Default sorting by newest
                query = query.order_by(WorkflowTemplate.created_at.desc())

            # Get total count with filters applied
            total = query.count()

            # Apply pagination
            page = request.page if request.page > 0 else 1
            page_size = min(request.page_size, 100) if request.page_size > 0 else 10

            templates = query.offset((page - 1) * page_size).limit(page_size).all()

            # Calculate pagination metadata
            total_pages = (total + page_size - 1) // page_size if total > 0 else 1
            has_next = page < total_pages
            has_prev = page > 1

            # Convert templates to protobuf format
            template_list = [
                self._template_to_marketplace_workflow(template) for template in templates
            ]

            logger.info(
                "marketplace_workflows_retrieved",
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
            )

            return workflow_pb2.GetMarketplaceWorkflowsResponse(
                success=True,
                message="Marketplace workflows retrieved successfully",
                workflows=template_list,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
                has_next=has_next,
                has_prev=has_prev,
                next_page=page + 1 if has_next else 0,
                prev_page=page - 1 if has_prev else 0,
            )

        except Exception as e:
            logger.error("get_marketplace_workflows_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to retrieve marketplace workflows: {str(e)}")
            return workflow_pb2.GetMarketplaceWorkflowsResponse(
                success=False,
                message=f"Failed to retrieve marketplace workflows: {str(e)}",
                workflows=[],
                total=0,
                page=request.page,
                page_size=request.page_size,
                total_pages=0,
                has_next=False,
                has_prev=False,
            )
        finally:
            db.close()

    def _template_to_marketplace_workflow(
        self, template: WorkflowTemplate
    ) -> workflow_pb2.MarketplaceWorkflow:
        """Convert a WorkflowTemplate model instance to a protobuf MarketplaceWorkflow message."""
        return workflow_pb2.MarketplaceWorkflow(
            id=str(template.id),
            name=template.name,
            description=template.description if template.description else "",
            image_url=template.image_url if template.image_url else "",
            workflow_url=template.workflow_url,
            builder_url=template.builder_url,
            start_nodes=(
                [json.dumps(node) for node in template.start_nodes] if template.start_nodes else []
            ),
            available_nodes=(
                [json.dumps(node) for node in template.available_nodes]
                if template.available_nodes
                else []
            ),
            category=template.category if template.category else "",
            tags=template.tags if template.tags else [],
            created_at=template.created_at.isoformat() if template.created_at else "",
            updated_at=template.updated_at.isoformat() if template.updated_at else "",
            owner_id=template.owner_id,
            average_rating=template.average_rating if template.average_rating else 0,
            use_count=template.use_count if template.use_count else 0,
            execution_count=template.execution_count if template.execution_count else 0,
            visibility="PUBLIC",
            version=template.version if template.version else "1.0.0",
            status=template.status if template.status else "active",
        )

    def updateWorkflowSettings(
        self, request: workflow_pb2.UpdateWorkflowSettingsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UpdateWorkflowSettingsResponse:
        db = self.get_db()
        logger.info(
            "update_workflow_settings_request",
            workflow_id=request.workflow_id,
            owner_id=request.owner.id,
        )
        try:
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()

            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found.")
                return workflow_pb2.UpdateWorkflowSettingsResponse(
                    success=False, message="Workflow not found."
                )

            if workflow.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("You are not the owner of this workflow.")
                return workflow_pb2.UpdateWorkflowSettingsResponse(
                    success=False, message="Permission denied."
                )

            updated_fields_count = 0
            if request.HasField("is_changes_marketplace"):  # Check if the optional field is set
                if workflow.visibility != WorkflowVisibilityEnum.PUBLIC:
                    msg = "Cannot set 'is_changes_marketplace' for a non-public workflow."
                    context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                    context.set_details(msg)
                    return workflow_pb2.UpdateWorkflowSettingsResponse(success=False, message=msg)

                if (
                    not workflow.workflow_template_id
                    or workflow.template_owner_id != workflow.owner_id
                ):
                    msg = (
                        "Cannot set 'is_changes_marketplace' as this workflow is not "
                        "properly linked to a user-owned public template."
                    )
                    context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                    context.set_details(msg)
                    return workflow_pb2.UpdateWorkflowSettingsResponse(success=False, message=msg)

                if workflow.is_changes_marketplace != request.is_changes_marketplace:
                    workflow.is_changes_marketplace = request.is_changes_marketplace
                    logger.info(
                        f"Workflow {workflow.id} is_changes_marketplace set to {request.is_changes_marketplace}"
                    )
                    updated_fields_count += 1

            if request.HasField("status"):
                if workflow.status != workflow_pb2.WorkflowStatus.Name(request.status).lower():
                    workflow.status = workflow_pb2.WorkflowStatus.Name(request.status).lower()
                    logger.info(f"Workflow {workflow.id} status set to {request.status}")
                    updated_fields_count += 1

            if updated_fields_count > 0:
                db.add(workflow)
                db.commit()
                db.refresh(workflow)
                message = "Workflow settings updated successfully."
            else:
                message = "No settings were changed."

            return workflow_pb2.UpdateWorkflowSettingsResponse(success=True, message=message)
        except Exception as e:
            if "db" in locals():
                db.rollback()
            logger.error("update_workflow_settings_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to update workflow settings: {str(e)}")
            return workflow_pb2.UpdateWorkflowSettingsResponse(
                success=False, message=f"Failed to update workflow settings: {str(e)}"
            )
        finally:
            db.close()

    def rateWorkflow(
        self, request: workflow_pb2.RateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.RateWorkflowResponse:
        """
        Rate a workflow and update its average rating.

        Args:
            request: Contains the workflow ID, user ID, and rating value
            context: gRPC service context

        Returns:
            Response containing success status, message, and updated average rating
        """
        db = self.get_db()
        try:
            logger.info(
                "rate_workflow_request",
                workflow_id=request.workflow_id,
                user_id=request.user_id,
                rating=request.rating,
            )

            # Validate rating value (between 1.0 and 5.0)
            if request.rating < 1.0 or request.rating > 5.0:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Rating must be between 1.0 and 5.0")
                return workflow_pb2.RateWorkflowResponse(
                    success=False, message="Rating must be between 1.0 and 5.0", average_rating=0.0
                )

            # Check if workflow exists
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found")
                return workflow_pb2.RateWorkflowResponse(
                    success=False,
                    message=f"Workflow with ID {request.workflow_id} not found",
                    average_rating=0.0,
                )

            # Check if user has already rated this workflow
            existing_rating = (
                db.query(WorkflowRating)
                .filter(
                    WorkflowRating.workflow_id == request.workflow_id,
                    WorkflowRating.user_id == request.user_id,
                )
                .first()
            )

            if existing_rating:
                # Update existing rating
                old_rating = existing_rating.rating
                existing_rating.rating = request.rating
                existing_rating.updated_at = datetime.utcnow()
                db.commit()
                logger.info(
                    "updated_workflow_rating",
                    workflow_id=request.workflow_id,
                    user_id=request.user_id,
                    old_rating=old_rating,
                    new_rating=request.rating,
                )
            else:
                # Create new rating
                new_rating = WorkflowRating(
                    workflow_id=request.workflow_id, user_id=request.user_id, rating=request.rating
                )
                db.add(new_rating)
                db.commit()
                logger.info(
                    "created_workflow_rating",
                    workflow_id=request.workflow_id,
                    user_id=request.user_id,
                    rating=request.rating,
                )

            # Calculate new average rating
            ratings = (
                db.query(WorkflowRating)
                .filter(WorkflowRating.workflow_id == request.workflow_id)
                .all()
            )
            total_rating = sum(r.rating for r in ratings)
            average_rating = total_rating / len(ratings) if ratings else 0.0

            # Update workflow with new average rating
            workflow.average_rating = average_rating
            workflow.updated_at = datetime.utcnow()
            db.commit()

            # If this is a workflow created from a template, update the template's rating too
            if workflow.workflow_template_id:
                template = (
                    db.query(WorkflowTemplate)
                    .filter(WorkflowTemplate.id == workflow.workflow_template_id)
                    .first()
                )

                if template:
                    # Get all ratings for all workflows created from this template
                    template_workflows = (
                        db.query(Workflow)
                        .filter(Workflow.workflow_template_id == template.id)
                        .all()
                    )

                    template_workflow_ids = [w.id for w in template_workflows]

                    if template_workflow_ids:
                        template_ratings = (
                            db.query(WorkflowRating)
                            .filter(WorkflowRating.workflow_id.in_(template_workflow_ids))
                            .all()
                        )

                        if template_ratings:
                            template_total_rating = sum(r.rating for r in template_ratings)
                            template_average_rating = template_total_rating / len(template_ratings)

                            template.average_rating = template_average_rating
                            template.updated_at = datetime.utcnow()
                            db.commit()

                            logger.info(
                                "updated_template_rating",
                                template_id=template.id,
                                average_rating=template_average_rating,
                            )

            return workflow_pb2.RateWorkflowResponse(
                success=True,
                message=f"Rating for workflow {workflow.name} updated successfully",
                average_rating=average_rating,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error rating workflow: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.RateWorkflowResponse(
                success=False, message="Failed to update workflow rating", average_rating=0.0
            )
        finally:
            db.close()

    def useWorkflow(
        self, request: workflow_pb2.UseWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UseWorkflowResponse:
        """
        Create a copy of a template workflow for a user and increment the template's use count.

        Args:
            request: Contains the workflow ID (template) and user ID
            context: gRPC service context

        Returns:
            Response containing success status, message, and updated use count
        """
        db = self.get_db()
        try:
            logger.info(
                "use_workflow_request", workflow_id=request.workflow_id, user_id=request.user_id
            )

            # Check if template workflow exists and is public
            template_workflow = (
                db.query(WorkflowTemplate)
                .filter(
                    WorkflowTemplate.id == request.workflow_id,
                    WorkflowTemplate.visibility == WorkflowVisibilityEnum.PUBLIC.value,
                    WorkflowTemplate.status == WorkflowStatusEnum.ACTIVE.value,
                )
                .first()
            )

            if not template_workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(
                    f"Public template workflow with ID {request.workflow_id} not found"
                )
                return workflow_pb2.UseWorkflowResponse(
                    success=False,
                    message=f"Public template workflow with ID {request.workflow_id} not found",
                    use_count=0,
                )

            # Create a new workflow based on the template
            new_workflow = Workflow(
                name=f"{template_workflow.name} - Copy",
                description=template_workflow.description,
                workflow_url=template_workflow.workflow_url,
                builder_url=template_workflow.builder_url,
                start_nodes=template_workflow.start_nodes if template_workflow.start_nodes else [],
                available_nodes=template_workflow.available_nodes
                if template_workflow.available_nodes
                else [],
                owner_id=request.user_id,
                user_ids=[request.user_id],
                owner_type="user",  # Assuming the user is of type "user"
                workflow_template_id=template_workflow.id,
                template_owner_id=template_workflow.owner_id,
                visibility=WorkflowVisibilityEnum.PRIVATE.value,  # Default to private for the copy
                category=template_workflow.category,
                tags=template_workflow.tags if template_workflow.tags else [],
                status=WorkflowStatusEnum.ACTIVE.value,
                version="1.0.0",  # Start with version 1.0.0 for the copy
            )

            db.add(new_workflow)

            # Increment the template's use count
            template_workflow.use_count += 1
            template_workflow.updated_at = datetime.utcnow()

            db.commit()
            db.refresh(new_workflow)

            logger.info(
                "workflow_created_from_template",
                new_workflow_id=new_workflow.id,
                template_id=template_workflow.id,
                user_id=request.user_id,
                new_use_count=template_workflow.use_count,
            )

            return workflow_pb2.UseWorkflowResponse(
                success=True,
                message=f"New workflow created from template {template_workflow.name}",
                use_count=template_workflow.use_count,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error using workflow template: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.UseWorkflowResponse(
                success=False, message="Failed to create workflow from template", use_count=0
            )
        finally:
            db.close()
